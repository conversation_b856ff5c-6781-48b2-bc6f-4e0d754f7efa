"""
LongBridge + Backtrader MACD回测系统
=====================================

这是一个完整的量化交易回测系统，具有以下功能：
1. 使用LongPort OpenAPI获取实时历史股票数据
2. 基于MACD指标实现量化交易策略
3. 使用Backtrader框架进行专业回测
4. 使用Plotly生成交互式可视化图表
5. 提供详细的交易统计和风险分析

主要组件：
- LongBridgeData: 数据下载器，负责从LongPort API获取历史K线数据
- MACDStrategy: MACD交易策略实现
- BacktestSystem: 回测系统主类，整合所有功能

作者: AI Assistant
版本: 1.0
"""

# 导入必要的库
import pandas as pd              # 数据处理和分析
import numpy as np               # 数值计算
import backtrader as bt          # 回测框架
import plotly.graph_objects as go # Plotly图表对象
import plotly.express as px      # Plotly快速绘图
from plotly.subplots import make_subplots  # 创建子图
from datetime import datetime, timedelta, date  # 日期时间处理
import os                        # 操作系统接口
from longport.openapi import QuoteContext, Config, Period, AdjustType  # LongPort API
import time                      # 时间相关功能
import warnings                  # 警告控制
warnings.filterwarnings('ignore')  # 忽略警告信息，保持输出清洁


class LongBridgeData:
    """
    LongBridge数据下载器
    ===================

    这个类负责从LongPort OpenAPI获取历史股票数据。
    LongPort是一个专业的港美股交易平台，提供丰富的市场数据API。

    功能特点：
    - 支持港股、美股、A股等多个市场
    - 提供实时和历史K线数据
    - 支持多种复权方式
    - 数据质量高，延迟低

    使用前需要：
    1. 在LongPort开发者中心申请API权限
    2. 设置环境变量：LONGPORT_APP_KEY, LONGPORT_APP_SECRET, LONGPORT_ACCESS_TOKEN
    3. 确保有相应市场的行情权限
    """

    def __init__(self):
        """
        初始化LongBridge连接

        从环境变量中读取API配置信息并创建行情上下文。
        需要预先设置以下环境变量：
        - LONGPORT_APP_KEY: 应用密钥
        - LONGPORT_APP_SECRET: 应用秘密
        - LONGPORT_ACCESS_TOKEN: 访问令牌

        Raises:
            Exception: 如果环境变量未设置或API连接失败
        """
        # 从环境变量加载配置
        self.config = Config.from_env()
        # 创建行情数据上下文，用于获取市场数据
        self.ctx = QuoteContext(self.config)
        
    def download_data(self, symbol, start_date, end_date):
        """
        下载历史K线数据
        ===============

        从LongPort API获取指定股票的历史K线数据，支持多个市场。

        Args:
            symbol (str): 股票代码，格式为 'ticker.market'
                         例如：'AAPL.US' (苹果-美股)
                              '00700.HK' (腾讯-港股)
                              '000001.SZ' (平安银行-深股)
            start_date (datetime): 开始日期，支持datetime对象
            end_date (datetime): 结束日期，支持datetime对象

        Returns:
            pandas.DataFrame: 包含OHLCV数据的DataFrame，列包括：
                - datetime: 日期时间索引
                - open: 开盘价
                - high: 最高价
                - low: 最低价
                - close: 收盘价
                - volume: 成交量

        Raises:
            ValueError: 当无法获取数据时抛出异常

        Note:
            - 使用前复权数据，确保价格连续性
            - 数据频率为日线（Period.Day）
            - 支持的历史数据范围因市场而异：
              * 美股：2010-06-01至今
              * 港股：2004-06-01至今
              * A股：1999-11-01至今
        """
        try:
            print(f"正在下载 {symbol} 从 {start_date} 到 {end_date} 的数据...")

            # 转换datetime为date对象，因为API需要date类型参数
            start_date_obj = start_date.date() if isinstance(start_date, datetime) else start_date
            end_date_obj = end_date.date() if isinstance(end_date, datetime) else end_date

            # 调用LongPort API获取历史K线数据
            # 参数说明：
            # - symbol: 股票代码
            # - Period.Day: 日线周期
            # - AdjustType.ForwardAdjust: 前复权，处理分红送股影响
            # - start_date_obj: 开始日期
            # - end_date_obj: 结束日期
            resp = self.ctx.history_candlesticks_by_date(
                symbol,
                Period.Day,  # 日线数据
                AdjustType.ForwardAdjust,  # 前复权
                start_date_obj,
                end_date_obj
            )
            
            # 检查API响应是否有效
            if not resp:
                raise ValueError(f"未能获取到 {symbol} 的数据")

            # 将API响应转换为pandas DataFrame
            # LongPort API返回的是Candlestick对象列表
            data = []
            for candle in resp:
                # 提取每根K线的OHLCV数据
                data.append({
                    'datetime': candle.timestamp,  # 时间戳（已经是datetime对象）
                    'open': float(candle.open),    # 开盘价（从Decimal转为float）
                    'high': float(candle.high),    # 最高价
                    'low': float(candle.low),      # 最低价
                    'close': float(candle.close),  # 收盘价
                    'volume': int(candle.volume)   # 成交量
                })

            # 创建DataFrame并设置时间索引
            df = pd.DataFrame(data)
            df.set_index('datetime', inplace=True)  # 将datetime设为索引
            df.sort_index(inplace=True)             # 按时间排序，确保数据顺序正确

            print(f"成功下载 {len(df)} 条数据")
            return df

        except Exception as e:
            # 捕获并处理所有可能的异常
            print(f"数据下载失败: {e}")
            return None


class MACDStrategy(bt.Strategy):
    """
    MACD交易策略
    ============

    基于MACD（Moving Average Convergence Divergence）指标的量化交易策略。
    MACD是一个趋势跟踪动量指标，通过计算两个不同周期的指数移动平均线的差值来判断趋势。

    策略原理：
    1. MACD线 = 快速EMA - 慢速EMA
    2. 信号线 = MACD线的EMA
    3. 直方图 = MACD线 - 信号线

    交易信号：
    - 买入信号：MACD线从下方穿越信号线（金叉）
    - 卖出信号：MACD线从上方穿越信号线（死叉）

    策略特点：
    - 适用于趋势性市场
    - 滞后性指标，适合中长期交易
    - 在震荡市场中可能产生较多假信号

    参数说明：
    - fast_period: 快速EMA周期，默认12
    - slow_period: 慢速EMA周期，默认26
    - signal_period: 信号线EMA周期，默认9
    - printlog: 是否打印交易日志
    """

    # 策略参数定义
    params = (
        ('fast_period', 12),     # 快线周期（短期EMA）
        ('slow_period', 26),     # 慢线周期（长期EMA）
        ('signal_period', 9),    # 信号线周期（MACD的EMA）
        ('printlog', True),      # 是否打印交易日志
    )
    
    def __init__(self):
        """
        策略初始化方法

        在这里定义所有需要的技术指标和交易信号。
        Backtrader会在策略开始前调用此方法进行初始化。
        """
        # 计算MACD指标（使用MACDHisto来获取完整的MACD指标，包括直方图）
        # MACDHisto包含三条线：macd线、signal线和histo直方图
        self.macd = bt.indicators.MACDHisto(
            self.data.close,                          # 使用收盘价计算
            period_me1=self.params.fast_period,      # 快速EMA周期
            period_me2=self.params.slow_period,      # 慢速EMA周期
            period_signal=self.params.signal_period  # 信号线EMA周期
        )

        # 提取MACD指标的各个组件，便于后续使用
        self.macd_line = self.macd.macd      # MACD主线（快EMA - 慢EMA）
        self.signal_line = self.macd.signal  # 信号线（MACD线的EMA）
        self.histogram = self.macd.histo     # 直方图（MACD线 - 信号线）

        # 创建交叉信号指标
        # CrossOver指标用于检测两条线的交叉：
        # 返回值 > 0：MACD线从下方穿越信号线（金叉，买入信号）
        # 返回值 < 0：MACD线从上方穿越信号线（死叉，卖出信号）
        self.crossover = bt.indicators.CrossOver(self.macd_line, self.signal_line)

        # 初始化交易状态变量
        self.order = None    # 当前待执行订单，用于避免重复下单
        self.trades = []     # 交易记录列表，用于存储交易详情
        
    def notify_order(self, order):
        """
        订单状态通知回调方法

        当订单状态发生变化时，Backtrader会自动调用此方法。
        用于跟踪订单执行情况和记录交易日志。

        Args:
            order: 订单对象，包含订单的所有信息
        """
        # 检查订单是否已完成执行
        if order.status in [order.Completed]:
            # 根据订单类型记录不同的执行信息
            if order.isbuy():
                # 买入订单执行完成
                self.log(f'买入执行, 价格: {order.executed.price:.2f}, '
                        f'数量: {order.executed.size}, '
                        f'手续费: {order.executed.comm:.2f}')
            else:
                # 卖出订单执行完成
                self.log(f'卖出执行, 价格: {order.executed.price:.2f}, '
                        f'数量: {order.executed.size}, '
                        f'手续费: {order.executed.comm:.2f}')
        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            # 订单被取消、保证金不足或被拒绝
            self.log('订单取消/保证金不足/拒绝')

        # 清除订单引用，允许下新订单
        self.order = None
    
    def notify_trade(self, trade):
        """
        交易完成通知回调方法

        当一个完整的交易（买入+卖出）完成时，Backtrader会调用此方法。
        用于记录交易盈亏和统计信息。

        Args:
            trade: 交易对象，包含交易的盈亏信息
        """
        # 只处理已关闭的交易（即买入和卖出都已完成）
        if not trade.isclosed:
            return

        # 记录交易盈亏信息
        self.log(f'交易利润, 毛利润 {trade.pnl:.2f}, 净利润 {trade.pnlcomm:.2f}')

        # 将交易详情添加到交易记录列表
        self.trades.append({
            'date': self.data.datetime.date(0),  # 交易完成日期
            'pnl': trade.pnl,                    # 毛利润（不含手续费）
            'pnlcomm': trade.pnlcomm             # 净利润（含手续费）
        })
    
    def next(self):
        """
        策略主逻辑方法

        这是策略的核心方法，Backtrader会在每个数据点（每个交易日）调用此方法。
        在这里实现具体的交易逻辑和信号判断。

        MACD策略逻辑：
        1. 检查是否有未完成的订单，避免重复下单
        2. 当没有持仓且出现金叉信号时，执行买入
        3. 当有持仓且出现死叉信号时，执行卖出
        """
        # 如果有未执行的订单，等待其完成，避免重复下单
        if self.order:
            return

        # 交易信号判断和执行

        # 买入信号：没有持仓 且 MACD金叉（MACD线从下方穿越信号线）
        if not self.position and self.crossover > 0:
            self.log(f'MACD金叉买入信号, 价格: {self.data.close[0]:.2f}')
            # 执行市价买入，买入全部可用资金
            self.order = self.buy()

        # 卖出信号：有持仓 且 MACD死叉（MACD线从上方穿越信号线）
        elif self.position and self.crossover < 0:
            self.log(f'MACD死叉卖出信号, 价格: {self.data.close[0]:.2f}')
            # 执行市价卖出，卖出全部持仓
            self.order = self.sell()

    def log(self, txt, dt=None):
        """
        日志输出方法

        用于输出策略运行过程中的重要信息，如交易信号、订单执行等。

        Args:
            txt (str): 要输出的日志内容
            dt (datetime, optional): 日志时间，默认使用当前数据点的时间
        """
        if self.params.printlog:
            # 获取当前数据点的日期，如果没有指定dt则使用当前日期
            dt = dt or self.datas[0].datetime.date(0)
            print(f'{dt.isoformat()}, {txt}')


class BacktestSystem:
    """
    回测系统主类
    ============

    这是整个回测系统的核心类，整合了数据获取、策略回测、结果分析和可视化等功能。

    主要功能：
    1. 数据管理：通过LongBridgeData获取历史数据
    2. 策略回测：使用Backtrader框架执行策略回测
    3. 结果分析：计算各种绩效指标和风险指标
    4. 可视化：生成交互式图表展示回测结果

    工作流程：
    1. 下载指定股票的历史数据
    2. 配置Backtrader回测环境
    3. 运行策略回测
    4. 分析回测结果
    5. 生成可视化图表

    支持的分析指标：
    - 总收益率、年化收益率
    - 夏普比率、最大回撤
    - 交易次数、胜率
    - 平均盈利、平均亏损
    """

    def __init__(self):
        """
        初始化回测系统

        创建数据下载器实例和结果存储字典。
        """
        # 创建LongBridge数据下载器实例
        self.data_downloader = LongBridgeData()
        # 存储不同股票的回测结果，key为股票代码，value为回测结果字典
        self.results = {}
        
    def run_backtest(self, symbol, start_date, end_date, initial_cash=100000):
        """
        运行完整的回测流程
        ==================

        这是回测系统的核心方法，执行完整的回测流程并返回详细结果。

        Args:
            symbol (str): 股票代码，格式如'AAPL.US', '00700.HK'
            start_date (datetime): 回测开始日期
            end_date (datetime): 回测结束日期
            initial_cash (float): 初始资金，默认10万

        Returns:
            dict: 包含完整回测结果的字典，包括：
                - 基本信息：股票代码、时间范围、资金情况
                - 收益指标：总收益率、夏普比率等
                - 风险指标：最大回撤等
                - 交易统计：交易次数、胜率、平均盈亏等
                - 原始数据：价格数据、策略实例等

        Returns None: 如果数据下载失败或回测出错
        """
        # 打印回测开始信息
        print(f"\n{'='*50}")
        print(f"开始回测 {symbol}")
        print(f"时间范围: {start_date.date()} 到 {end_date.date()}")
        print(f"初始资金: ${initial_cash:,.2f}")
        print(f"{'='*50}")

        # 第一步：下载历史数据
        df = self.data_downloader.download_data(symbol, start_date, end_date)
        if df is None or len(df) == 0:
            print("数据下载失败，无法进行回测")
            return None
        
        # 第二步：创建Backtrader回测引擎
        # Cerebro是Backtrader的核心引擎，负责协调所有回测组件
        cerebro = bt.Cerebro()

        # 第三步：添加数据源
        # 将pandas DataFrame转换为Backtrader可识别的数据格式
        data = bt.feeds.PandasData(dataname=df)
        cerebro.adddata(data)

        # 第四步：添加交易策略
        # 将我们定义的MACD策略添加到回测引擎
        cerebro.addstrategy(MACDStrategy)

        # 第五步：设置初始资金
        # 设置回测开始时的账户资金
        cerebro.broker.setcash(initial_cash)

        # 第六步：设置交易成本
        # 设置手续费为0.1%，模拟真实交易成本
        cerebro.broker.setcommission(commission=0.001)

        # 第七步：添加性能分析器
        # 这些分析器会自动计算各种回测指标
        cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name="trades")    # 交易分析
        cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name="sharpe")      # 夏普比率
        cerebro.addanalyzer(bt.analyzers.DrawDown, _name="drawdown")       # 回撤分析
        cerebro.addanalyzer(bt.analyzers.Returns, _name="returns")         # 收益分析
        
        # 第八步：执行回测
        print('\n开始运行回测...')
        # 记录回测开始时的账户价值
        start_value = cerebro.broker.getvalue()
        # 运行回测，返回策略实例列表
        results = cerebro.run()
        # 记录回测结束时的账户价值
        end_value = cerebro.broker.getvalue()

        # 第九步：提取回测结果
        # 获取策略实例（results是列表，我们只有一个策略）
        strategy = results[0]

        # 计算基本收益统计
        # 总收益率 = (期末价值 - 期初价值) / 期初价值 * 100%
        total_return = ((end_value - start_value) / start_value) * 100

        # 从各个分析器中提取详细统计数据
        trade_analyzer = strategy.analyzers.trades.get_analysis()      # 交易统计
        sharpe_ratio = strategy.analyzers.sharpe.get_analysis().get('sharperatio', 0)  # 夏普比率
        drawdown = strategy.analyzers.drawdown.get_analysis()          # 回撤统计
        returns_analyzer = strategy.analyzers.returns.get_analysis()   # 收益统计（暂未使用）
        
        # 第十步：整理回测结果
        # 将所有回测数据和统计指标整理成字典格式，便于后续分析和展示
        results_dict = {
            # 基本信息
            'symbol': symbol,                    # 股票代码
            'start_date': start_date,           # 回测开始日期
            'end_date': end_date,               # 回测结束日期
            'initial_cash': initial_cash,       # 初始资金

            # 资金变化
            'start_value': start_value,         # 期初账户价值
            'end_value': end_value,             # 期末账户价值
            'total_return': total_return,       # 总收益率(%)

            # 风险收益指标
            'sharpe_ratio': sharpe_ratio,       # 夏普比率（风险调整后收益）
            'max_drawdown': drawdown.get('max', {}).get('drawdown', 0),  # 最大回撤(%)

            # 交易统计
            'trade_count': trade_analyzer.get('total', {}).get('total', 0),      # 总交易次数
            'win_count': trade_analyzer.get('won', {}).get('total', 0),          # 盈利交易次数
            'lose_count': trade_analyzer.get('lost', {}).get('total', 0),        # 亏损交易次数
            'win_rate': 0,                      # 胜率(%)，稍后计算
            'avg_win': trade_analyzer.get('won', {}).get('pnl', {}).get('average', 0),   # 平均盈利
            'avg_lose': trade_analyzer.get('lost', {}).get('pnl', {}).get('average', 0), # 平均亏损

            # 原始数据（用于绘图和进一步分析）
            'data': df,                         # 价格数据DataFrame
            'strategy': strategy                # 策略实例（包含交易记录等）
        }

        # 计算胜率：盈利交易次数 / 总交易次数 * 100%
        if results_dict['trade_count'] > 0:
            results_dict['win_rate'] = (results_dict['win_count'] / results_dict['trade_count']) * 100

        # 将结果存储到实例变量中，便于后续访问
        self.results[symbol] = results_dict

        # 打印回测结果摘要
        self.print_results(results_dict)

        return results_dict
    
    def print_results(self, results):
        """
        打印回测结果摘要
        ================

        以格式化的方式在控制台输出回测的关键指标和统计信息。

        Args:
            results (dict): 包含回测结果的字典
        """
        print(f"\n{'='*30} 回测结果 {'='*30}")

        # 基本信息
        print(f"股票代码: {results['symbol']}")
        print(f"初始资金: ${results['initial_cash']:,.2f}")
        print(f"最终资金: ${results['end_value']:,.2f}")

        # 收益指标
        print(f"总收益率: {results['total_return']:.2f}%")

        # 夏普比率可能为None，需要特殊处理
        sharpe_ratio = results['sharpe_ratio']
        if sharpe_ratio is not None:
            print(f"夏普比率: {sharpe_ratio:.4f}")
        else:
            print(f"夏普比率: N/A")  # 数据不足时无法计算

        # 风险指标
        print(f"最大回撤: {results['max_drawdown']:.2f}%")

        # 交易统计
        print(f"交易次数: {results['trade_count']}")
        print(f"胜利次数: {results['win_count']}")
        print(f"失败次数: {results['lose_count']}")
        print(f"胜率: {results['win_rate']:.2f}%")

        # 平均盈亏（只在有相应交易时显示）
        if results['avg_win'] != 0:
            print(f"平均盈利: ${results['avg_win']:.2f}")
        if results['avg_lose'] != 0:
            print(f"平均亏损: ${results['avg_lose']:.2f}")

        print(f"{'='*70}")
    
    def plot_results(self, symbol):
        """
        使用Plotly绘制交互式回测结果图表
        ================================

        生成包含价格走势、MACD指标和交易信号的综合可视化图表。

        Args:
            symbol (str): 要绘制的股票代码

        Returns:
            plotly.graph_objects.Figure: Plotly图表对象，可以显示或保存

        图表包含三个子图：
        1. 价格走势图：K线图 + 买卖信号点
        2. MACD指标图：MACD线和信号线
        3. MACD直方图：显示MACD和信号线的差值
        """
        # 检查是否存在该股票的回测结果
        if symbol not in self.results:
            print(f"未找到 {symbol} 的回测结果")
            return

        # 获取回测结果和数据
        results = self.results[symbol]
        df = results['data'].copy()  # 复制数据，避免修改原始数据
        strategy = results['strategy']  # 策略实例（暂未使用，但保留用于扩展）

        # 重新计算MACD指标用于绘图
        # 注意：这里重新计算是为了确保绘图数据的一致性
        exp1 = df['close'].ewm(span=12).mean()    # 12日指数移动平均
        exp2 = df['close'].ewm(span=26).mean()    # 26日指数移动平均
        df['macd'] = exp1 - exp2                  # MACD线 = 快EMA - 慢EMA
        df['signal'] = df['macd'].ewm(span=9).mean()  # 信号线 = MACD的9日EMA
        df['histogram'] = df['macd'] - df['signal']   # 直方图 = MACD线 - 信号线
        
        # 识别买卖信号点
        # 通过检测MACD线和信号线的交叉来确定买卖信号
        buy_signals = []   # 存储买入信号：(日期, 价格)
        sell_signals = []  # 存储卖出信号：(日期, 价格)

        # 遍历数据，检测交叉信号
        # 从第二个数据点开始，因为需要比较前一天的状态
        for i in range(1, len(df)):
            # 买入信号：MACD线从下方穿越信号线（金叉）
            # 条件：当前MACD > 信号线 且 前一天MACD <= 信号线
            if df['macd'].iloc[i] > df['signal'].iloc[i] and df['macd'].iloc[i-1] <= df['signal'].iloc[i-1]:
                buy_signals.append((df.index[i], df['close'].iloc[i]))

            # 卖出信号：MACD线从上方穿越信号线（死叉）
            # 条件：当前MACD < 信号线 且 前一天MACD >= 信号线
            elif df['macd'].iloc[i] < df['signal'].iloc[i] and df['macd'].iloc[i-1] >= df['signal'].iloc[i-1]:
                sell_signals.append((df.index[i], df['close'].iloc[i]))
        
        # 创建多子图布局
        # 使用Plotly的make_subplots创建包含3个子图的垂直布局
        fig = make_subplots(
            rows=3, cols=1,                    # 3行1列的布局
            shared_xaxes=True,                 # 共享x轴（时间轴）
            vertical_spacing=0.03,             # 子图间的垂直间距
            row_width=[0.2, 0.2, 0.6],        # 各子图的高度比例
            subplot_titles=(                   # 各子图的标题
                f'{symbol} 价格走势与交易信号',
                'MACD指标',
                'MACD柱状图'
            )
        )
        
        # 第一个子图：价格走势图（K线图）
        # 添加K线图（蜡烛图）显示OHLC数据
        fig.add_trace(
            go.Candlestick(
                x=df.index,           # x轴：时间
                open=df['open'],      # 开盘价
                high=df['high'],      # 最高价
                low=df['low'],        # 最低价
                close=df['close'],    # 收盘价
                name='价格',          # 图例名称
                showlegend=False      # 不在图例中显示（避免图例过多）
            ),
            row=1, col=1  # 添加到第1行第1列
        )

        # 添加买入信号标记
        if buy_signals:
            # 将买入信号列表分解为日期和价格两个列表
            buy_dates, buy_prices = zip(*buy_signals)
            fig.add_trace(
                go.Scatter(
                    x=buy_dates,         # x轴：买入信号日期
                    y=buy_prices,        # y轴：买入价格
                    mode='markers',      # 只显示标记点，不连线
                    marker=dict(
                        symbol='triangle-up',  # 向上三角形标记
                        size=10,               # 标记大小
                        color='green'          # 绿色表示买入
                    ),
                    name='买入信号',     # 图例名称
                    showlegend=True      # 在图例中显示
                ),
                row=1, col=1  # 添加到第1行第1列
            )
        
        # 添加卖出信号标记
        if sell_signals:
            # 将卖出信号列表分解为日期和价格两个列表
            sell_dates, sell_prices = zip(*sell_signals)
            fig.add_trace(
                go.Scatter(
                    x=sell_dates,        # x轴：卖出信号日期
                    y=sell_prices,       # y轴：卖出价格
                    mode='markers',      # 只显示标记点，不连线
                    marker=dict(
                        symbol='triangle-down',  # 向下三角形标记
                        size=10,                 # 标记大小
                        color='red'              # 红色表示卖出
                    ),
                    name='卖出信号',     # 图例名称
                    showlegend=True      # 在图例中显示
                ),
                row=1, col=1  # 添加到第1行第1列
            )

        # 第二个子图：MACD指标线图
        # 添加MACD主线
        fig.add_trace(
            go.Scatter(
                x=df.index,                      # x轴：时间
                y=df['macd'],                    # y轴：MACD值
                line=dict(color='blue', width=2), # 蓝色线条，宽度为2
                name='MACD',                     # 图例名称
                showlegend=True                  # 在图例中显示
            ),
            row=2, col=1  # 添加到第2行第1列
        )

        # 添加信号线
        fig.add_trace(
            go.Scatter(
                x=df.index,                     # x轴：时间
                y=df['signal'],                 # y轴：信号线值
                line=dict(color='red', width=2), # 红色线条，宽度为2
                name='Signal',                  # 图例名称
                showlegend=True                 # 在图例中显示
            ),
            row=2, col=1  # 添加到第2行第1列
        )
        
        # 第三个子图：MACD直方图
        # 根据直方图值的正负设置颜色：正值为绿色，负值为红色
        colors = ['green' if val >= 0 else 'red' for val in df['histogram']]
        fig.add_trace(
            go.Bar(
                x=df.index,              # x轴：时间
                y=df['histogram'],       # y轴：直方图值（MACD - Signal）
                marker_color=colors,     # 柱子颜色：绿色/红色
                name='MACD Histogram',   # 图例名称
                showlegend=True          # 在图例中显示
            ),
            row=3, col=1  # 添加到第3行第1列
        )

        # 更新图表布局和样式
        # 处理可能为None的夏普比率
        sharpe_text = f'{results["sharpe_ratio"]:.4f}' if results["sharpe_ratio"] is not None else 'N/A'

        fig.update_layout(
            # 设置图表标题，包含关键回测指标
            title={
                'text': f'{symbol} MACD策略回测结果<br>' +
                       f'<sub>总收益率: {results["total_return"]:.2f}% | ' +
                       f'夏普比率: {sharpe_text} | ' +
                       f'最大回撤: {results["max_drawdown"]:.2f}% | ' +
                       f'胜率: {results["win_rate"]:.1f}%</sub>',
                'x': 0.5,           # 标题居中
                'xanchor': 'center'
            },
            xaxis_rangeslider_visible=False,  # 隐藏x轴范围滑块（避免界面混乱）
            height=800,                       # 图表总高度
            showlegend=True,                  # 显示图例
            # 图例设置：水平排列，位于图表上方
            legend=dict(
                orientation="h",    # 水平排列
                yanchor="bottom",   # 底部对齐
                y=1.02,            # 位于图表上方
                xanchor="right",   # 右对齐
                x=1                # 右边界
            )
        )
        
        # 更新各子图的坐标轴标签
        fig.update_xaxes(title_text="日期", row=3, col=1)      # 只在最下方显示日期标签
        fig.update_yaxes(title_text="价格", row=1, col=1)      # 第1个子图y轴：价格
        fig.update_yaxes(title_text="MACD值", row=2, col=1)    # 第2个子图y轴：MACD值
        fig.update_yaxes(title_text="柱状图", row=3, col=1)    # 第3个子图y轴：柱状图

        return fig


def main():
    """
    主函数 - 回测系统使用示例
    ========================

    演示如何使用回测系统进行完整的策略回测流程。
    包括参数设置、回测执行、结果展示等步骤。

    这个示例展示了对苹果股票(AAPL.US)在2023年的MACD策略回测。
    """
    # 第一步：创建回测系统实例
    backtest_system = BacktestSystem()

    # 第二步：设置回测参数
    symbol = "AAPL.US"                    # 股票代码：苹果公司
    start_date = datetime(2023, 1, 1)     # 回测开始日期：2023年1月1日
    end_date = datetime(2024, 1, 1)       # 回测结束日期：2024年1月1日
    initial_cash = 100000                 # 初始资金：10万美元

    # 第三步：执行回测
    results = backtest_system.run_backtest(
        symbol=symbol,
        start_date=start_date,
        end_date=end_date,
        initial_cash=initial_cash
    )

    # 第四步：处理回测结果
    if results:
        # 生成可视化图表
        fig = backtest_system.plot_results(symbol)
        if fig:
            # 显示交互式图表
            fig.show()

            # 可选：保存图表为HTML文件
            # fig.write_html(f"{symbol}_backtest_results.html")
            print(f"\n回测完成! 图表已显示。")
        else:
            print("图表生成失败")
    else:
        print("回测失败")


if __name__ == "__main__":
    # 运行示例
    print("LongBridge + Backtrader MACD回测系统")
    print("="*50)
    print("功能特点:")
    print("1. 使用LongBridge API获取实时历史数据")
    print("2. 基于MACD指标的量化交易策略")
    print("3. 完整的回测框架和风险分析")
    print("4. 使用Plotly进行专业级可视化")
    print("5. 详细的交易统计和绩效指标")
    print("="*50)
    
    # 运行主程序
    main()
