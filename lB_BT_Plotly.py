"""
LongBridge + Backtrader MACD回测系统
基于LongPort OpenAPI获取历史数据，使用Backtrader进行MACD策略回测，并使用Plotly绘图
"""

import pandas as pd
import numpy as np
import backtrader as bt
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from datetime import datetime, timedelta
import os
from longport.openapi import QuoteContext, Config, Period, AdjustType
import time
import warnings
warnings.filterwarnings('ignore')


class LongBridgeData:
    """LongBridge数据下载器"""
    
    def __init__(self):
        """初始化LongBridge连接"""
        self.config = Config.from_env()
        self.ctx = QuoteContext(self.config)
        
    def download_data(self, symbol, start_date, end_date):
        """
        下载历史K线数据
        
        Args:
            symbol: 股票代码，如'AAPL.US', '00700.HK'
            start_date: 开始日期 (datetime对象)
            end_date: 结束日期 (datetime对象)
            
        Returns:
            pandas.DataFrame: 包含OHLCV数据的DataFrame
        """
        try:
            print(f"正在下载 {symbol} 从 {start_date} 到 {end_date} 的数据...")
            
            # 获取历史K线数据
            resp = self.ctx.history_candlesticks_by_date(
                symbol=symbol,
                period=Period.Day,  # 日线数据
                start=start_date,
                end=end_date,
                adjust_type=AdjustType.ForwardAdjusted  # 前复权
            )
            
            if not resp:
                raise ValueError(f"未能获取到 {symbol} 的数据")
            
            # 转换为DataFrame
            data = []
            for candle in resp:
                data.append({
                    'datetime': datetime.fromtimestamp(candle.timestamp),
                    'open': float(candle.open),
                    'high': float(candle.high),
                    'low': float(candle.low),
                    'close': float(candle.close),
                    'volume': int(candle.volume)
                })
            
            df = pd.DataFrame(data)
            df.set_index('datetime', inplace=True)
            df.sort_index(inplace=True)
            
            print(f"成功下载 {len(df)} 条数据")
            return df
            
        except Exception as e:
            print(f"数据下载失败: {e}")
            return None


class MACDStrategy(bt.Strategy):
    """MACD策略"""
    
    params = (
        ('fast_period', 12),     # 快线周期
        ('slow_period', 26),     # 慢线周期
        ('signal_period', 9),    # 信号线周期
        ('printlog', True),      # 是否打印日志
    )
    
    def __init__(self):
        # 计算MACD指标
        self.macd = bt.indicators.MACD(
            self.data.close,
            period_me1=self.params.fast_period,
            period_me2=self.params.slow_period,
            period_signal=self.params.signal_period
        )
        
        # MACD组件
        self.macd_line = self.macd.macd
        self.signal_line = self.macd.signal
        self.histogram = self.macd.histo
        
        # 交易信号
        self.crossover = bt.indicators.CrossOver(self.macd_line, self.signal_line)
        
        # 记录交易
        self.order = None
        self.trades = []
        
    def notify_order(self, order):
        """订单状态通知"""
        if order.status in [order.Completed]:
            if order.isbuy():
                self.log(f'买入执行, 价格: {order.executed.price:.2f}, '
                        f'数量: {order.executed.size}, '
                        f'手续费: {order.executed.comm:.2f}')
            else:
                self.log(f'卖出执行, 价格: {order.executed.price:.2f}, '
                        f'数量: {order.executed.size}, '
                        f'手续费: {order.executed.comm:.2f}')
        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            self.log('订单取消/保证金不足/拒绝')
        
        self.order = None
    
    def notify_trade(self, trade):
        """交易完成通知"""
        if not trade.isclosed:
            return
        
        self.log(f'交易利润, 毛利润 {trade.pnl:.2f}, 净利润 {trade.pnlcomm:.2f}')
        
        # 记录交易详情
        self.trades.append({
            'date': self.data.datetime.date(0),
            'pnl': trade.pnl,
            'pnlcomm': trade.pnlcomm
        })
    
    def next(self):
        """策略主逻辑"""
        # 检查是否有未执行订单
        if self.order:
            return
        
        # MACD金叉买入信号
        if not self.position and self.crossover > 0:
            self.log(f'MACD金叉买入信号, 价格: {self.data.close[0]:.2f}')
            self.order = self.buy()
        
        # MACD死叉卖出信号
        elif self.position and self.crossover < 0:
            self.log(f'MACD死叉卖出信号, 价格: {self.data.close[0]:.2f}')
            self.order = self.sell()
    
    def log(self, txt, dt=None):
        """日志输出"""
        if self.params.printlog:
            dt = dt or self.datas[0].datetime.date(0)
            print(f'{dt.isoformat()}, {txt}')


class BacktestSystem:
    """回测系统主类"""
    
    def __init__(self):
        """初始化回测系统"""
        self.data_downloader = LongBridgeData()
        self.results = {}
        
    def run_backtest(self, symbol, start_date, end_date, initial_cash=100000):
        """
        运行回测
        
        Args:
            symbol: 股票代码
            start_date: 开始日期 (datetime对象)
            end_date: 结束日期 (datetime对象)
            initial_cash: 初始资金
            
        Returns:
            dict: 回测结果
        """
        print(f"\n{'='*50}")
        print(f"开始回测 {symbol}")
        print(f"时间范围: {start_date.date()} 到 {end_date.date()}")
        print(f"初始资金: ${initial_cash:,.2f}")
        print(f"{'='*50}")
        
        # 1. 下载数据
        df = self.data_downloader.download_data(symbol, start_date, end_date)
        if df is None or len(df) == 0:
            print("数据下载失败，无法进行回测")
            return None
        
        # 2. 创建Backtrader引擎
        cerebro = bt.Cerebro()
        
        # 3. 添加数据
        data = bt.feeds.PandasData(dataname=df)
        cerebro.adddata(data)
        
        # 4. 添加策略
        cerebro.addstrategy(MACDStrategy)
        
        # 5. 设置初始资金
        cerebro.broker.setcash(initial_cash)
        
        # 6. 设置手续费 (0.1%)
        cerebro.broker.setcommission(commission=0.001)
        
        # 7. 添加分析器
        cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name="trades")
        cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name="sharpe")
        cerebro.addanalyzer(bt.analyzers.DrawDown, _name="drawdown")
        cerebro.addanalyzer(bt.analyzers.Returns, _name="returns")
        
        # 8. 运行回测
        print('\n开始运行回测...')
        start_value = cerebro.broker.getvalue()
        results = cerebro.run()
        end_value = cerebro.broker.getvalue()
        
        # 9. 获取结果
        strategy = results[0]
        
        # 计算收益统计
        total_return = ((end_value - start_value) / start_value) * 100
        
        trade_analyzer = strategy.analyzers.trades.get_analysis()
        sharpe_ratio = strategy.analyzers.sharpe.get_analysis().get('sharperatio', 0)
        drawdown = strategy.analyzers.drawdown.get_analysis()
        returns_analyzer = strategy.analyzers.returns.get_analysis()
        
        # 整理结果
        results_dict = {
            'symbol': symbol,
            'start_date': start_date,
            'end_date': end_date,
            'initial_cash': initial_cash,
            'start_value': start_value,
            'end_value': end_value,
            'total_return': total_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': drawdown.get('max', {}).get('drawdown', 0),
            'trade_count': trade_analyzer.get('total', {}).get('total', 0),
            'win_count': trade_analyzer.get('won', {}).get('total', 0),
            'lose_count': trade_analyzer.get('lost', {}).get('total', 0),
            'win_rate': 0,
            'avg_win': trade_analyzer.get('won', {}).get('pnl', {}).get('average', 0),
            'avg_lose': trade_analyzer.get('lost', {}).get('pnl', {}).get('average', 0),
            'data': df,
            'strategy': strategy
        }
        
        # 计算胜率
        if results_dict['trade_count'] > 0:
            results_dict['win_rate'] = (results_dict['win_count'] / results_dict['trade_count']) * 100
        
        self.results[symbol] = results_dict
        
        # 打印结果摘要
        self.print_results(results_dict)
        
        return results_dict
    
    def print_results(self, results):
        """打印回测结果"""
        print(f"\n{'='*30} 回测结果 {'='*30}")
        print(f"股票代码: {results['symbol']}")
        print(f"初始资金: ${results['initial_cash']:,.2f}")
        print(f"最终资金: ${results['end_value']:,.2f}")
        print(f"总收益率: {results['total_return']:.2f}%")
        print(f"夏普比率: {results['sharpe_ratio']:.4f}")
        print(f"最大回撤: {results['max_drawdown']:.2f}%")
        print(f"交易次数: {results['trade_count']}")
        print(f"胜利次数: {results['win_count']}")
        print(f"失败次数: {results['lose_count']}")
        print(f"胜率: {results['win_rate']:.2f}%")
        if results['avg_win'] != 0:
            print(f"平均盈利: ${results['avg_win']:.2f}")
        if results['avg_lose'] != 0:
            print(f"平均亏损: ${results['avg_lose']:.2f}")
        print(f"{'='*70}")
    
    def plot_results(self, symbol):
        """使用Plotly绘制回测结果"""
        if symbol not in self.results:
            print(f"未找到 {symbol} 的回测结果")
            return
        
        results = self.results[symbol]
        df = results['data'].copy()
        strategy = results['strategy']
        
        # 计算MACD指标
        exp1 = df['close'].ewm(span=12).mean()
        exp2 = df['close'].ewm(span=26).mean()
        df['macd'] = exp1 - exp2
        df['signal'] = df['macd'].ewm(span=9).mean()
        df['histogram'] = df['macd'] - df['signal']
        
        # 获取买卖信号点
        buy_signals = []
        sell_signals = []
        
        # 模拟策略信号
        for i in range(1, len(df)):
            if df['macd'].iloc[i] > df['signal'].iloc[i] and df['macd'].iloc[i-1] <= df['signal'].iloc[i-1]:
                buy_signals.append((df.index[i], df['close'].iloc[i]))
            elif df['macd'].iloc[i] < df['signal'].iloc[i] and df['macd'].iloc[i-1] >= df['signal'].iloc[i-1]:
                sell_signals.append((df.index[i], df['close'].iloc[i]))
        
        # 创建子图
        fig = make_subplots(
            rows=3, cols=1,
            shared_xaxes=True,
            vertical_spacing=0.03,
            row_width=[0.2, 0.2, 0.6],
            subplot_titles=(f'{symbol} 价格走势与交易信号', 'MACD指标', 'MACD柱状图')
        )
        
        # 1. 价格走势图
        fig.add_trace(
            go.Candlestick(
                x=df.index,
                open=df['open'],
                high=df['high'],
                low=df['low'],
                close=df['close'],
                name='价格',
                showlegend=False
            ),
            row=1, col=1
        )
        
        # 买入信号
        if buy_signals:
            buy_dates, buy_prices = zip(*buy_signals)
            fig.add_trace(
                go.Scatter(
                    x=buy_dates,
                    y=buy_prices,
                    mode='markers',
                    marker=dict(
                        symbol='triangle-up',
                        size=10,
                        color='green'
                    ),
                    name='买入信号',
                    showlegend=True
                ),
                row=1, col=1
            )
        
        # 卖出信号
        if sell_signals:
            sell_dates, sell_prices = zip(*sell_signals)
            fig.add_trace(
                go.Scatter(
                    x=sell_dates,
                    y=sell_prices,
                    mode='markers',
                    marker=dict(
                        symbol='triangle-down',
                        size=10,
                        color='red'
                    ),
                    name='卖出信号',
                    showlegend=True
                ),
                row=1, col=1
            )
        
        # 2. MACD线图
        fig.add_trace(
            go.Scatter(
                x=df.index,
                y=df['macd'],
                line=dict(color='blue', width=2),
                name='MACD',
                showlegend=True
            ),
            row=2, col=1
        )
        
        fig.add_trace(
            go.Scatter(
                x=df.index,
                y=df['signal'],
                line=dict(color='red', width=2),
                name='Signal',
                showlegend=True
            ),
            row=2, col=1
        )
        
        # 3. MACD柱状图
        colors = ['green' if val >= 0 else 'red' for val in df['histogram']]
        fig.add_trace(
            go.Bar(
                x=df.index,
                y=df['histogram'],
                marker_color=colors,
                name='MACD Histogram',
                showlegend=True
            ),
            row=3, col=1
        )
        
        # 更新布局
        fig.update_layout(
            title={
                'text': f'{symbol} MACD策略回测结果<br>' + 
                       f'<sub>总收益率: {results["total_return"]:.2f}% | ' +
                       f'夏普比率: {results["sharpe_ratio"]:.4f} | ' +
                       f'最大回撤: {results["max_drawdown"]:.2f}% | ' +
                       f'胜率: {results["win_rate"]:.1f}%</sub>',
                'x': 0.5,
                'xanchor': 'center'
            },
            xaxis_rangeslider_visible=False,
            height=800,
            showlegend=True,
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.02,
                xanchor="right",
                x=1
            )
        )
        
        # 更新x轴标签
        fig.update_xaxes(title_text="日期", row=3, col=1)
        fig.update_yaxes(title_text="价格", row=1, col=1)
        fig.update_yaxes(title_text="MACD值", row=2, col=1)
        fig.update_yaxes(title_text="柱状图", row=3, col=1)
        
        return fig


def main():
    """主函数示例"""
    # 创建回测系统
    backtest_system = BacktestSystem()
    
    # 设置回测参数
    symbol = "AAPL.US"  # 苹果股票
    start_date = datetime(2023, 1, 1)
    end_date = datetime(2024, 1, 1)
    initial_cash = 100000  # 10万美元
    
    # 运行回测
    results = backtest_system.run_backtest(
        symbol=symbol,
        start_date=start_date, 
        end_date=end_date,
        initial_cash=initial_cash
    )
    
    if results:
        # 绘制结果
        fig = backtest_system.plot_results(symbol)
        if fig:
            fig.show()
            
            # 可选：保存图表
            # fig.write_html(f"{symbol}_backtest_results.html")
            print(f"\n回测完成! 图表已显示。")
        else:
            print("图表生成失败")
    else:
        print("回测失败")


if __name__ == "__main__":
    # 运行示例
    print("LongBridge + Backtrader MACD回测系统")
    print("="*50)
    print("功能特点:")
    print("1. 使用LongBridge API获取实时历史数据")
    print("2. 基于MACD指标的量化交易策略")
    print("3. 完整的回测框架和风险分析")
    print("4. 使用Plotly进行专业级可视化")
    print("5. 详细的交易统计和绩效指标")
    print("="*50)
    
    # 运行主程序
    main()
